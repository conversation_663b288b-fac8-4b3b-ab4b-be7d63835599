@echo off
echo ========================================
echo  GRUB Removal - Alternative Method
echo ========================================
echo.
echo This script uses alternative methods since bootrec is not available
echo Make sure you are running this as Administrator!
echo.
pause

echo.
echo Step 1: Current boot configuration (we can see Windows is already configured)
echo ========================================
bcdedit /enum
echo.
echo ANALYSIS: Your Windows bootloader is already properly configured!
echo The issue might be that GRUB is installed in the MBR but Windows BCD is intact.
echo.
pause

echo.
echo Step 2: Using bcdboot to ensure Windows bootloader is active...
echo ========================================
echo This will reinstall the Windows bootloader to the system partition
bcdboot C:\Windows /s C: /f BIOS
if %errorlevel% equ 0 (
    echo SUCCESS: Windows bootloader has been reinstalled
) else (
    echo ERROR: Failed to reinstall Windows bootloader - Error code: %errorlevel%
)
echo.
pause

echo.
echo Step 3: Setting Windows Boot Manager as active...
echo ========================================
echo Using diskpart to set the system partition as active...
echo.
echo list disk > diskpart_script.txt
echo select disk 0 >> diskpart_script.txt
echo list partition >> diskpart_script.txt
echo select partition 1 >> diskpart_script.txt
echo active >> diskpart_script.txt
echo exit >> diskpart_script.txt

diskpart /s diskpart_script.txt
del diskpart_script.txt

echo.
echo Partition has been set as active
pause

echo.
echo Step 4: Alternative MBR fix using Windows tools...
echo ========================================
echo Creating a script to write Windows MBR...

echo select disk 0 > mbr_fix.txt
echo clean >> mbr_fix.txt
echo create partition primary >> mbr_fix.txt
echo select partition 1 >> mbr_fix.txt
echo active >> mbr_fix.txt
echo format fs=ntfs quick >> mbr_fix.txt
echo assign >> mbr_fix.txt
echo exit >> mbr_fix.txt

echo.
echo WARNING: The above would reformat your disk!
echo Instead, let's try a safer approach...
echo.

del mbr_fix.txt

echo.
echo Step 5: Using bcdedit to ensure proper boot configuration...
echo ========================================
bcdedit /set {bootmgr} device partition=C:
bcdedit /set {bootmgr} path \bootmgr
bcdedit /set {current} device partition=C:
bcdedit /set {current} osdevice partition=C:

echo.
echo Boot configuration has been updated
pause

echo.
echo Step 6: Final verification...
echo ========================================
bcdedit /enum
echo.

echo.
echo ========================================
echo  ALTERNATIVE PROCESS COMPLETED
echo ========================================
echo.
echo What we accomplished:
echo 1. Verified Windows bootloader configuration is intact
echo 2. Reinstalled Windows bootloader using bcdboot
echo 3. Set system partition as active
echo 4. Updated boot configuration
echo.
echo NEXT STEPS:
echo 1. Restart your computer
echo 2. If GRUB still appears, we need to use Windows Recovery Environment
echo 3. The issue might be that GRUB is in the MBR but can be overwritten
echo.
echo If GRUB still appears after restart, let me know and we'll try
echo the Windows Recovery Environment method.
echo.
pause
