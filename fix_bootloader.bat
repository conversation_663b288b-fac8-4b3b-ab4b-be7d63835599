@echo off
echo ========================================
echo  GRUB Removal and Windows Bootloader Restoration
echo ========================================
echo.
echo This script will remove GRUB and restore Windows bootloader
echo Make sure you are running this as Administrator!
echo.
pause

echo.
echo Step 1: Checking current boot configuration...
echo ========================================
bcdedit /enum
echo.
pause

echo.
echo Step 2: Fixing Master Boot Record (MBR)...
echo ========================================
bootrec /fixmbr
if %errorlevel% equ 0 (
    echo SUCCESS: MBR has been fixed
) else (
    echo ERROR: Failed to fix MBR
)
echo.
pause

echo.
echo Step 3: Fixing Boot Sector...
echo ========================================
bootrec /fixboot
if %errorlevel% equ 0 (
    echo SUCCESS: Boot sector has been fixed
) else (
    echo ERROR: Failed to fix boot sector
)
echo.
pause

echo.
echo Step 4: Scanning for Windows installations...
echo ========================================
bootrec /scanos
echo.
pause

echo.
echo Step 5: Rebuilding Boot Configuration Data (BCD)...
echo ========================================
echo When prompted, type Y to add Windows installation to boot list
bootrec /rebuildbcd
echo.
pause

echo.
echo Step 6: Additional BCD rebuild (if needed)...
echo ========================================
bcdboot C:\Windows
if %errorlevel% equ 0 (
    echo SUCCESS: BCD has been rebuilt
) else (
    echo ERROR: Failed to rebuild BCD
)
echo.

echo.
echo Step 7: Final boot configuration check...
echo ========================================
bcdedit /enum
echo.

echo.
echo ========================================
echo  PROCESS COMPLETED
echo ========================================
echo.
echo If all steps completed successfully:
echo 1. Restart your computer
echo 2. GRUB should no longer appear
echo 3. Windows should boot directly
echo.
echo If you encounter any errors, please note them down
echo and we can try alternative methods.
echo.
pause
