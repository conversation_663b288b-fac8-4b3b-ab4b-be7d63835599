# PowerShell Script to Remove "gnub" from Windows Startup
# Run this script as Administrator for full access

Write-Host "=== Removing 'gnub' from Windows Startup ===" -ForegroundColor Green
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "WARNING: Not running as Administrator. Some operations may fail." -ForegroundColor Yellow
    Write-Host "For best results, run PowerS<PERSON> as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# 1. Check and remove from Registry - Current User
Write-Host "1. Checking Current User Registry..." -ForegroundColor Cyan
$regPaths = @(
    "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run",
    "HKCU:\Software\Microsoft\Windows\CurrentVersion\RunOnce"
)

foreach ($regPath in $regPaths) {
    try {
        $entries = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
        if ($entries) {
            $gnubEntries = $entries.PSObject.Properties | Where-Object { $_.Name -like "*gnub*" -or $_.Value -like "*gnub*" }
            foreach ($entry in $gnubEntries) {
                Write-Host "Found gnub entry in $regPath : $($entry.Name) = $($entry.Value)" -ForegroundColor Red
                try {
                    Remove-ItemProperty -Path $regPath -Name $entry.Name -Force
                    Write-Host "Removed: $($entry.Name)" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to remove: $($entry.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } catch {
        Write-Host "Could not access $regPath" -ForegroundColor Yellow
    }
}

# 2. Check and remove from Registry - Local Machine (requires admin)
Write-Host ""
Write-Host "2. Checking Local Machine Registry..." -ForegroundColor Cyan
$regPathsLM = @(
    "HKLM:\Software\Microsoft\Windows\CurrentVersion\Run",
    "HKLM:\Software\Microsoft\Windows\CurrentVersion\RunOnce",
    "HKLM:\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\Run",
    "HKLM:\Software\WOW6432Node\Microsoft\Windows\CurrentVersion\RunOnce"
)

foreach ($regPath in $regPathsLM) {
    try {
        $entries = Get-ItemProperty -Path $regPath -ErrorAction SilentlyContinue
        if ($entries) {
            $gnubEntries = $entries.PSObject.Properties | Where-Object { $_.Name -like "*gnub*" -or $_.Value -like "*gnub*" }
            foreach ($entry in $gnubEntries) {
                Write-Host "Found gnub entry in $regPath : $($entry.Name) = $($entry.Value)" -ForegroundColor Red
                try {
                    Remove-ItemProperty -Path $regPath -Name $entry.Name -Force
                    Write-Host "Removed: $($entry.Name)" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to remove: $($entry.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } catch {
        Write-Host "Could not access $regPath (may need admin rights)" -ForegroundColor Yellow
    }
}

# 3. Check Startup Folders
Write-Host ""
Write-Host "3. Checking Startup Folders..." -ForegroundColor Cyan
$startupFolders = @(
    "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Startup",
    "$env:ALLUSERSPROFILE\Microsoft\Windows\Start Menu\Programs\Startup"
)

foreach ($folder in $startupFolders) {
    if (Test-Path $folder) {
        $gnubFiles = Get-ChildItem -Path $folder | Where-Object { $_.Name -like "*gnub*" }
        foreach ($file in $gnubFiles) {
            Write-Host "Found gnub file in startup folder: $($file.FullName)" -ForegroundColor Red
            try {
                Remove-Item -Path $file.FullName -Force
                Write-Host "Removed: $($file.Name)" -ForegroundColor Green
            } catch {
                Write-Host "Failed to remove: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# 4. Check Task Scheduler
Write-Host ""
Write-Host "4. Checking Task Scheduler..." -ForegroundColor Cyan
try {
    $tasks = Get-ScheduledTask | Where-Object { $_.TaskName -like "*gnub*" -or $_.Description -like "*gnub*" }
    foreach ($task in $tasks) {
        Write-Host "Found gnub scheduled task: $($task.TaskName)" -ForegroundColor Red
        try {
            Unregister-ScheduledTask -TaskName $task.TaskName -Confirm:$false
            Write-Host "Removed task: $($task.TaskName)" -ForegroundColor Green
        } catch {
            Write-Host "Failed to remove task: $($task.TaskName) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Could not access Task Scheduler" -ForegroundColor Yellow
}

# 5. Display current startup programs for manual review
Write-Host ""
Write-Host "5. Current Startup Programs (for manual review):" -ForegroundColor Cyan
Write-Host "You can also disable startup programs manually through:" -ForegroundColor Yellow
Write-Host "- Task Manager > Startup tab" -ForegroundColor Yellow
Write-Host "- Windows Settings > Apps > Startup" -ForegroundColor Yellow
Write-Host ""

Write-Host "=== Script Complete ===" -ForegroundColor Green
Write-Host "If 'gnub' still appears at startup, you may need to:" -ForegroundColor Yellow
Write-Host "1. Check Task Manager > Startup tab manually" -ForegroundColor Yellow
Write-Host "2. Check Windows Settings > Apps > Startup" -ForegroundColor Yellow
Write-Host "3. Look for the program in installed programs and uninstall it" -ForegroundColor Yellow
Write-Host "4. Run a malware scan if this is unwanted software" -ForegroundColor Yellow
