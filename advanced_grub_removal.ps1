# Advanced GRUB Removal Script
# This script uses multiple methods to remove GRUB

Write-Host "========================================" -ForegroundColor Cyan
Write-Host " Advanced GRUB Removal Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: Must run as Administrator!" -ForegroundColor Red
    exit 1
}

Write-Host "Method 1: Using WMI to identify and fix boot disk" -ForegroundColor Yellow
Write-Host "=================================================" -ForegroundColor Yellow

# Get system disk information
try {
    $systemDisk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "C:" }
    $diskNumber = (Get-Partition -DriveLetter C).DiskNumber
    
    Write-Host "System disk identified: Disk $diskNumber" -ForegroundColor Green
    Write-Host "Drive C: Size: $([math]::Round($systemDisk.Size/1GB, 2)) GB" -ForegroundColor Green
    
    # Get all partitions on the system disk
    $partitions = Get-Partition -DiskNumber $diskNumber
    Write-Host "Partitions on system disk:" -ForegroundColor Cyan
    foreach ($partition in $partitions) {
        Write-Host "  Partition $($partition.PartitionNumber): $($partition.Type) - $([math]::Round($partition.Size/1GB, 2)) GB" -ForegroundColor White
    }
} catch {
    Write-Host "Error getting disk information: $($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Method 2: Setting system partition as active using PowerShell" -ForegroundColor Yellow
Write-Host "=============================================================" -ForegroundColor Yellow

try {
    # Find the system partition (usually the first partition)
    $systemPartition = Get-Partition -DiskNumber $diskNumber | Where-Object { $_.Type -eq "System" -or $_.PartitionNumber -eq 1 }
    
    if ($systemPartition) {
        Write-Host "Found system partition: Partition $($systemPartition.PartitionNumber)" -ForegroundColor Green
        
        # Set partition as active using diskpart
        $diskpartScript = @"
select disk $diskNumber
select partition $($systemPartition.PartitionNumber)
active
exit
"@
        
        $diskpartScript | Out-File -FilePath "temp_diskpart.txt" -Encoding ASCII
        $result = diskpart /s temp_diskpart.txt
        Remove-Item "temp_diskpart.txt" -Force
        
        Write-Host "Partition set as active" -ForegroundColor Green
    } else {
        Write-Host "Could not identify system partition automatically" -ForegroundColor Yellow
        Write-Host "Trying partition 1..." -ForegroundColor Yellow
        
        $diskpartScript = @"
select disk $diskNumber
select partition 1
active
exit
"@
        
        $diskpartScript | Out-File -FilePath "temp_diskpart.txt" -Encoding ASCII
        diskpart /s temp_diskpart.txt | Out-Null
        Remove-Item "temp_diskpart.txt" -Force
        
        Write-Host "Partition 1 set as active" -ForegroundColor Green
    }
} catch {
    Write-Host "Error setting partition as active: $($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Method 3: Multiple bcdboot attempts with different parameters" -ForegroundColor Yellow
Write-Host "============================================================" -ForegroundColor Yellow

$bcdbootCommands = @(
    "bcdboot C:\Windows /s C: /f BIOS",
    "bcdboot C:\Windows /s C: /f ALL",
    "bcdboot C:\Windows /s C:",
    "bcdboot C:\Windows"
)

foreach ($cmd in $bcdbootCommands) {
    Write-Host "Trying: $cmd" -ForegroundColor Cyan
    try {
        $result = cmd /c "$cmd 2>&1"
        Write-Host $result -ForegroundColor White
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS with: $cmd" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Seconds 1
}

Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Method 4: BCD Store manipulation" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Yellow

try {
    # Export current BCD for backup
    Write-Host "Creating BCD backup..." -ForegroundColor Cyan
    bcdedit /export "C:\BCD_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    
    # Set boot manager properties
    Write-Host "Configuring boot manager..." -ForegroundColor Cyan
    bcdedit /set "{bootmgr}" device "partition=C:"
    bcdedit /set "{bootmgr}" path "\bootmgr"
    
    # Ensure current entry points to correct locations
    Write-Host "Configuring Windows loader..." -ForegroundColor Cyan
    bcdedit /set "{current}" device "partition=C:"
    bcdedit /set "{current}" osdevice "partition=C:"
    bcdedit /set "{current}" path "\WINDOWS\system32\winload.exe"
    
    Write-Host "BCD configuration updated" -ForegroundColor Green
} catch {
    Write-Host "Error updating BCD: $($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Method 5: Final verification and cleanup" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow

Write-Host "Current boot configuration:" -ForegroundColor Cyan
bcdedit /enum

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " ADVANCED GRUB REMOVAL COMPLETED" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Summary of actions taken:" -ForegroundColor Green
Write-Host "1. Identified system disk and partitions" -ForegroundColor White
Write-Host "2. Set system partition as active" -ForegroundColor White
Write-Host "3. Tried multiple bcdboot configurations" -ForegroundColor White
Write-Host "4. Updated BCD store settings" -ForegroundColor White
Write-Host "5. Created BCD backup" -ForegroundColor White
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. RESTART your computer immediately" -ForegroundColor White
Write-Host "2. If GRUB still appears, we'll try EasyBCD or manual hex editing" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
