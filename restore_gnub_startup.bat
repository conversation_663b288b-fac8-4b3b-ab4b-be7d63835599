@echo off
echo ===================================
echo Restoring 'gnub' to Windows Startup
echo ===================================
echo.

echo WARNING: This will restore gnub startup entries.
echo Press Ctrl+C to cancel, or any key to continue...
pause >nul
echo.

echo Adding registry entries...
echo.

REM Add to Current User Run key
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v gnub /t REG_SZ /d "gnub.exe" /f 2>nul
if %errorlevel%==0 echo Added gnub to Current User Run registry

REM Note: We don't restore to RunOnce as those are one-time entries
REM Note: We don't restore to HKLM entries as we don't know the original path

echo.
echo Registry entries restored.
echo.
echo NOTE: If gnub was originally in startup folders or had a specific path,
echo you may need to manually restore those entries.
echo.
echo To manually add gnub to startup:
echo 1. Find the gnub executable file
echo 2. Copy it or create a shortcut
echo 3. Place in: %APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup\
echo.
echo OR use Task Scheduler or Windows Settings to add it back.

echo.
echo ===================================
echo Restore completed!
echo.
echo gnub has been added back to startup registry.
echo.
echo To verify gnub will start with Windows:
echo 1. Open Task Manager (Ctrl+Shift+Esc)
echo 2. Go to Startup tab
echo 3. Look for gnub entry
echo.
echo OR
echo.
echo 1. Open Windows Settings
echo 2. Go to Apps ^> Startup
echo 3. Look for gnub entry
echo ===================================
echo.
pause
