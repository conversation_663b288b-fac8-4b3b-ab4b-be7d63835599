# PowerShell Script to <PERSON>ore "gnub" to Windows Startup
# This reverses the actions of remove_gnub_startup.ps1

Write-Host "=== Restoring 'gnub' to Windows Startup ===" -ForegroundColor Green
Write-Host ""

# Warning message
Write-Host "WARNING: This will restore gnub to startup!" -ForegroundColor Red
Write-Host "Press Ctrl+C to cancel, or any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
Write-Host ""

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

if (-not (Test-Administrator)) {
    Write-Host "WARNING: Not running as Administrator. Some operations may fail." -ForegroundColor Yellow
    Write-Host "For best results, run PowerShell as Administrator." -ForegroundColor Yellow
    Write-Host ""
}

# 1. Add to Current User Registry
Write-Host "1. Adding to Current User Registry..." -ForegroundColor Cyan
try {
    $regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    Set-ItemProperty -Path $regPath -Name "gnub" -Value "gnub.exe" -Force
    Write-Host "Added gnub to Current User Run registry" -ForegroundColor Green
} catch {
    Write-Host "Failed to add to Current User registry: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Optionally add to Local Machine Registry (requires admin)
Write-Host ""
Write-Host "2. Adding to Local Machine Registry (requires admin)..." -ForegroundColor Cyan
if (Test-Administrator) {
    try {
        $regPath = "HKLM:\Software\Microsoft\Windows\CurrentVersion\Run"
        Set-ItemProperty -Path $regPath -Name "gnub" -Value "gnub.exe" -Force
        Write-Host "Added gnub to Local Machine Run registry" -ForegroundColor Green
    } catch {
        Write-Host "Failed to add to Local Machine registry: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Skipping Local Machine registry (requires administrator privileges)" -ForegroundColor Yellow
}

# 3. Information about manual restoration
Write-Host ""
Write-Host "3. Manual Restoration Options:" -ForegroundColor Cyan
Write-Host "If you need to restore gnub with a specific path or to startup folders:" -ForegroundColor Yellow
Write-Host "- Find the original gnub executable" -ForegroundColor Yellow
Write-Host "- Copy or create a shortcut to:" -ForegroundColor Yellow
Write-Host "  User Startup: $env:APPDATA\Microsoft\Windows\Start Menu\Programs\Startup\" -ForegroundColor Yellow
Write-Host "  All Users Startup: $env:ALLUSERSPROFILE\Microsoft\Windows\Start Menu\Programs\Startup\" -ForegroundColor Yellow
Write-Host ""

# 4. Create a scheduled task as alternative
Write-Host "4. Alternative: Create Scheduled Task..." -ForegroundColor Cyan
$createTask = Read-Host "Do you want to create a scheduled task for gnub? (y/n)"
if ($createTask -eq 'y' -or $createTask -eq 'Y') {
    try {
        $action = New-ScheduledTaskAction -Execute "gnub.exe"
        $trigger = New-ScheduledTaskTrigger -AtLogOn
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
        Register-ScheduledTask -TaskName "gnub_startup" -Action $action -Trigger $trigger -Settings $settings -Description "Restored gnub startup task"
        Write-Host "Created scheduled task 'gnub_startup'" -ForegroundColor Green
    } catch {
        Write-Host "Failed to create scheduled task: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== Restoration Complete ===" -ForegroundColor Green
Write-Host "gnub has been restored to startup. You can verify by checking:" -ForegroundColor Yellow
Write-Host "- Task Manager > Startup tab" -ForegroundColor Yellow
Write-Host "- Windows Settings > Apps > Startup" -ForegroundColor Yellow
Write-Host ""
Write-Host "Note: If gnub doesn't start, you may need to:" -ForegroundColor Yellow
Write-Host "1. Verify the gnub.exe path is correct" -ForegroundColor Yellow
Write-Host "2. Update the registry entry with the full path to gnub.exe" -ForegroundColor Yellow
Write-Host "3. Manually add gnub to the startup folder" -ForegroundColor Yellow
