# GRUB Removal and Windows Bootloader Restoration Script
# Run this script as Administrator

Write-Host "========================================" -ForegroundColor Cyan
Write-Host " GRUB Removal and Windows Bootloader Restoration" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click on PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Running as Administrator - Good!" -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 1: Checking current boot configuration..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    bcdedit /enum
    Write-Host "Boot configuration displayed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error reading boot configuration: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 2: Fixing Master Boot Record (MBR)..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $result = cmd /c "bootrec /fixmbr 2>&1"
    Write-Host $result
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: MBR has been fixed" -ForegroundColor Green
    } else {
        Write-Host "WARNING: MBR fix returned exit code $LASTEXITCODE" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error fixing MBR: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 3: Fixing Boot Sector..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $result = cmd /c "bootrec /fixboot 2>&1"
    Write-Host $result
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Boot sector has been fixed" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Boot sector fix returned exit code $LASTEXITCODE" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error fixing boot sector: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 4: Scanning for Windows installations..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $result = cmd /c "bootrec /scanos 2>&1"
    Write-Host $result
    Write-Host "Scan completed" -ForegroundColor Green
} catch {
    Write-Host "Error scanning for Windows installations: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 5: Rebuilding Boot Configuration Data (BCD)..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
Write-Host "When prompted, type Y to add Windows installation to boot list" -ForegroundColor Cyan
try {
    $result = cmd /c "bootrec /rebuildbcd 2>&1"
    Write-Host $result
    Write-Host "BCD rebuild process completed" -ForegroundColor Green
} catch {
    Write-Host "Error rebuilding BCD: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 6: Additional BCD rebuild (ensuring Windows boot files)..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    $result = cmd /c "bcdboot C:\Windows 2>&1"
    Write-Host $result
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Windows boot files have been restored" -ForegroundColor Green
    } else {
        Write-Host "WARNING: bcdboot returned exit code $LASTEXITCODE" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error running bcdboot: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""
Read-Host "Press Enter to continue"

Write-Host ""
Write-Host "Step 7: Final boot configuration check..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Yellow
try {
    bcdedit /enum
    Write-Host "Final boot configuration displayed" -ForegroundColor Green
} catch {
    Write-Host "Error reading final boot configuration: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host " PROCESS COMPLETED" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "If all steps completed successfully:" -ForegroundColor Green
Write-Host "1. Restart your computer" -ForegroundColor White
Write-Host "2. GRUB should no longer appear" -ForegroundColor White
Write-Host "3. Windows should boot directly" -ForegroundColor White
Write-Host ""
Write-Host "If you encountered any errors, please note them down" -ForegroundColor Yellow
Write-Host "and we can try alternative methods." -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit"
