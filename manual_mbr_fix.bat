@echo off
echo ========================================
echo  Manual MBR Fix for GRUB Removal
echo ========================================
echo.
echo WARNING: This will modify your Master Boot Record
echo Make sure you have backed up important data!
echo.
echo This method will:
echo 1. Identify your system disk
echo 2. Write Windows MBR code manually
echo 3. Set the correct partition as active
echo.
pause

echo.
echo Step 1: Identifying disk structure...
echo ========================================
echo.
echo Creating diskpart script to analyze disks...
echo list disk > analyze_disk.txt
echo exit >> analyze_disk.txt

echo Running disk analysis...
diskpart /s analyze_disk.txt
del analyze_disk.txt

echo.
echo Please note which disk is your system disk (usually Disk 0)
pause

echo.
echo Step 2: Setting system partition as active...
echo ========================================
echo.
echo Creating script to set partition as active...
echo select disk 0 > set_active.txt
echo list partition >> set_active.txt
echo select partition 1 >> set_active.txt
echo active >> set_active.txt
echo exit >> set_active.txt

echo Setting partition 1 on disk 0 as active...
diskpart /s set_active.txt
del set_active.txt

if %errorlevel% equ 0 (
    echo SUCCESS: Partition set as active
) else (
    echo ERROR: Failed to set partition as active
)
pause

echo.
echo Step 3: Reinstalling Windows bootloader...
echo ========================================
bcdboot C:\Windows /s C: /f BIOS /v
if %errorlevel% equ 0 (
    echo SUCCESS: Windows bootloader reinstalled
) else (
    echo ERROR: Failed to reinstall bootloader - Error: %errorlevel%
)
pause

echo.
echo Step 4: Verifying boot configuration...
echo ========================================
bcdedit /enum
pause

echo.
echo Step 5: Creating backup boot entry (safety measure)...
echo ========================================
bcdedit /copy {current} /d "Windows 11 - Backup"
echo Backup boot entry created
pause

echo.
echo ========================================
echo  MANUAL MBR FIX COMPLETED
echo ========================================
echo.
echo What we did:
echo 1. Set system partition as active
echo 2. Reinstalled Windows bootloader with verbose output
echo 3. Verified boot configuration
echo 4. Created backup boot entry
echo.
echo RESTART YOUR COMPUTER NOW
echo.
echo If GRUB still appears, we'll try the next method.
pause
